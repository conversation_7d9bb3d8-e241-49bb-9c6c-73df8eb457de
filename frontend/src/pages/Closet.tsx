import { useState, useEffect } from 'react';
import { Plus, Camera, Link, Upload, Shirt, Search, Grid, List, SlidersHorizontal, X, Loader2, AlertCircle, ChevronRight } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { GlassInput } from '@/components/ui/glass-input';
import { cn } from '@/lib/utils';
import { FilePickerService, hasCamera } from '@/services/filePicker';
import { FileUploadResult } from '@/types/user';
import { emitClosetItemAdded, onClosetItemAdded } from '@/lib/eventBus';

interface ClothingItem {
  id: string;
  name: string;
  category: string;
  color: string;
  brand?: string;
  season?: string;
  image?: string;
}

export const Closet = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isUploadingFile, setIsUploadingFile] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [items, setItems] = useState<ClothingItem[]>([]);

  const categories = ['All', 'Tops', 'Bottoms', 'Outerwear', 'Dresses', 'Shoes', 'Accessories'];

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Subscribe to cross-app closet item additions
  useEffect(() => {
    const unsubscribe = onClosetItemAdded((item) => {
      setItems((prev) => [item, ...prev]);
    });
    return unsubscribe;
  }, []);

  // File upload handlers
  const handleFileUpload = async (result: FileUploadResult) => {
    if (result.success && result.file) {
      console.log('File uploaded successfully:', result.file.name);
      // Optimistically create a new closet item and broadcast it
      const optimisticItem: ClothingItem = {
        id: `temp-${Date.now()}`,
        name: result.file.name.replace(/\.[^/.]+$/, '') || 'New Item',
        category: selectedCategory === 'All' ? 'Uncategorized' : selectedCategory,
        color: 'Unknown',
        image: result.url,
      };
      setItems((prev) => [optimisticItem, ...prev]);
      emitClosetItemAdded(optimisticItem);

      // Here you would typically save the file and item metadata to your backend
      // and then reconcile the optimistic item with the saved item (id, category, color, etc.)

      setIsAddModalOpen(false);
      setUploadError(null);
    } else {
      setUploadError(result.error || 'Upload failed');
    }
    setIsUploadingFile(false);
  };

  const handleCameraCapture = async () => {
    setIsUploadingFile(true);
    setUploadError(null);
    try {
      const result = await FilePickerService.captureFromCamera();
      await handleFileUpload(result);
    } catch (error) {
      setUploadError('Camera capture failed');
      setIsUploadingFile(false);
    }
  };

  const handleGallerySelect = async () => {
    setIsUploadingFile(true);
    setUploadError(null);
    try {
      const result = await FilePickerService.selectFromGallery();
      await handleFileUpload(result);
    } catch (error) {
      setUploadError('Gallery selection failed');
      setIsUploadingFile(false);
    }
  };

  const handleFileSystemBrowse = async () => {
    setIsUploadingFile(true);
    setUploadError(null);
    try {
      const result = await FilePickerService.browseFiles();
      await handleFileUpload(result);
    } catch (error) {
      setUploadError('File selection failed');
      setIsUploadingFile(false);
    }
  };

  const filteredClothes = items.filter(item => {
    const matchesCategory = selectedCategory === 'All' || item.category === selectedCategory;
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.color.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (item.brand && item.brand.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-zara-white relative overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="w-full h-full" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, hsl(var(--zara-charcoal)) 1px, transparent 1px)`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      {/* Header */}
      <div className={cn(
        "sticky top-0 z-30 bg-zara-white/80 backdrop-blur-xl border-b border-zara-light-gray transition-all duration-800 ease-liquid",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
      )}>
        <div className="px-6 pt-16 pb-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="zara-hero">Your Closet</h1>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="p-2 glass-subtle rounded-xl hover:glass-light transition-all duration-300"
              >
                {viewMode === 'grid' ? <List size={20} /> : <Grid size={20} />}
              </button>
              <button className="p-2 glass-subtle rounded-xl hover:glass-light transition-all duration-300">
                <SlidersHorizontal size={20} />
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="mb-6">
            <div className="relative">
              <Search size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-zara-dark-gray" />
              <GlassInput
                variant="default"
                placeholder="Search your closet..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4"
              />
            </div>
          </div>

          {/* Enhanced Category Filter */}
          <div className="overflow-x-auto">
            <div className="flex space-x-3 pb-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={cn(
                    "px-6 py-3 rounded-full whitespace-nowrap transition-all duration-300 ease-glass",
                    selectedCategory === category
                      ? 'glass-strong text-zara-charcoal scale-105'
                      : 'glass-subtle text-zara-dark-gray hover:glass-light hover:scale-102'
                  )}
                >
                  <span className="zara-body font-medium">{category}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className={cn(
        "relative z-10 transition-all duration-1000 ease-liquid",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
      )}>

        {/* Clothing Display */}
        <div className="px-6 pb-32">
          {filteredClothes.length > 0 ? (
            <div className={cn(
              "transition-all duration-500",
              viewMode === 'grid'
                ? "grid grid-cols-2 gap-6"
                : "space-y-4"
            )}>
              {filteredClothes.map((item) => (
                <GlassCard
                  key={item.id}
                  variant="product"
                  className={cn(
                    "glass-morphing cursor-pointer transition-all duration-500 ease-premium",
                    viewMode === 'grid' ? "aspect-[3/4] p-4" : "p-6"
                  )}
                  onClick={() => console.log('Item selected:', item.name)}
                >
                  {viewMode === 'grid' ? (
                    <div className="h-full flex flex-col">
                      <div className="flex-1 glass-subtle rounded-2xl flex items-center justify-center mb-4 overflow-hidden">
                        {item.image ? (
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover fashion-image"
                          />
                        ) : (
                          <div className="w-20 h-20 glass-medium rounded-xl flex items-center justify-center">
                            <Shirt size={24} className="text-zara-dark-gray" />
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <h3 className="zara-subtitle truncate">{item.name}</h3>
                        <p className="zara-body text-zara-dark-gray">{item.color}</p>
                        {item.brand && (
                          <p className="zara-caption">{item.brand}</p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 glass-subtle rounded-xl flex items-center justify-center flex-shrink-0">
                        <Shirt size={20} className="text-zara-dark-gray" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <h3 className="zara-subtitle">{item.name}</h3>
                        <p className="zara-body text-zara-dark-gray">{item.color}</p>
                        <p className="zara-caption">{item.category}</p>
                      </div>
                      <ChevronRight size={20} className="text-zara-medium-gray" />
                    </div>
                  )}
                </GlassCard>
              ))}
            </div>
          ) : (
            <GlassCard variant="hero" className="p-12">
              <div className="text-center space-y-6">
                <div className="w-20 h-20 mx-auto glass-medium rounded-full flex items-center justify-center">
                  <Shirt size={32} className="text-zara-dark-gray" />
                </div>
                <div className="space-y-3">
                  <h3 className="zara-title">
                    {searchQuery ? 'No Items Found' : 'Your Closet is Empty'}
                  </h3>
                  <p className="zara-body text-zara-dark-gray max-w-md mx-auto leading-relaxed">
                    {searchQuery
                      ? `No items match "${searchQuery}". Try adjusting your search or browse different categories.`
                      : 'Start building your wardrobe by adding your first item. Organize your clothes and create amazing outfits.'
                    }
                  </p>
                </div>
                <div className="pt-4">
                  <GlassButton
                    onClick={() => setIsAddModalOpen(true)}
                    variant="primary"
                    size="lg"
                  >
                    Add Your First Item
                  </GlassButton>
                </div>
              </div>
            </GlassCard>
          )}
        </div>

      </div>

      {/* Enhanced Floating Action Button */}
      <button
        onClick={() => setIsAddModalOpen(true)}
        className={cn(
          "fixed bottom-24 right-6 z-40 w-16 h-16 glass-strong rounded-full",
          "flex items-center justify-center transition-all duration-300 ease-glass",
          "hover:scale-110 hover:glass-prominent active:scale-95",
          "shadow-lg hover:shadow-xl",
          isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}
        style={{ animationDelay: '1000ms' }}
      >
        <Plus size={24} className="text-zara-charcoal" />
      </button>

      {/* Enhanced Full-Screen Add Item Modal */}
      {isAddModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div
            className="absolute inset-0 bg-zara-charcoal/20 backdrop-blur-xl"
            onClick={() => setIsAddModalOpen(false)}
          />

          <div className="relative w-full max-w-lg mx-6 animate-glass-scale-in">
            <GlassCard variant="modal" className="p-8">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-8">
                <h3 className="zara-title">Add New Item</h3>
                <button
                  onClick={() => setIsAddModalOpen(false)}
                  className="p-2 glass-subtle rounded-full hover:glass-light transition-all duration-300"
                >
                  <X size={20} className="text-zara-dark-gray" />
                </button>
              </div>

              {/* Upload Options */}
              <div className="space-y-4 mb-8">
                {/* Camera Capture */}
                <button
                  onClick={handleCameraCapture}
                  disabled={isUploadingFile || !hasCamera()}
                  className={cn(
                    "w-full p-6 glass-subtle rounded-2xl flex items-center space-x-4 transition-all duration-300 group",
                    hasCamera() && !isUploadingFile ? "hover:glass-light" : "opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className="p-3 glass-medium rounded-xl group-hover:scale-110 transition-transform duration-300">
                    {isUploadingFile ? (
                      <Loader2 size={24} className="text-zara-charcoal animate-spin" />
                    ) : (
                      <Camera size={24} className="text-zara-charcoal" />
                    )}
                  </div>
                  <div className="text-left flex-1">
                    <p className="zara-subtitle">Take Photo</p>
                    <p className="zara-body text-zara-dark-gray">
                      {hasCamera() ? 'Capture with camera' : 'Camera not available'}
                    </p>
                  </div>
                  {!isUploadingFile && hasCamera() && (
                    <ChevronRight size={20} className="text-zara-medium-gray group-hover:translate-x-1 transition-transform duration-300" />
                  )}
                </button>

                {/* Gallery Selection */}
                <button
                  onClick={handleGallerySelect}
                  disabled={isUploadingFile}
                  className={cn(
                    "w-full p-6 glass-subtle rounded-2xl flex items-center space-x-4 transition-all duration-300 group",
                    !isUploadingFile ? "hover:glass-light" : "opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className="p-3 glass-medium rounded-xl group-hover:scale-110 transition-transform duration-300">
                    {isUploadingFile ? (
                      <Loader2 size={24} className="text-zara-charcoal animate-spin" />
                    ) : (
                      <Upload size={24} className="text-zara-charcoal" />
                    )}
                  </div>
                  <div className="text-left flex-1">
                    <p className="zara-subtitle">Upload Photo</p>
                    <p className="zara-body text-zara-dark-gray">Choose from gallery</p>
                  </div>
                  {!isUploadingFile && (
                    <ChevronRight size={20} className="text-zara-medium-gray group-hover:translate-x-1 transition-transform duration-300" />
                  )}
                </button>

                {/* File System Browser */}
                <button
                  onClick={handleFileSystemBrowse}
                  disabled={isUploadingFile}
                  className={cn(
                    "w-full p-6 glass-subtle rounded-2xl flex items-center space-x-4 transition-all duration-300 group",
                    !isUploadingFile ? "hover:glass-light" : "opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className="p-3 glass-medium rounded-xl group-hover:scale-110 transition-transform duration-300">
                    {isUploadingFile ? (
                      <Loader2 size={24} className="text-zara-charcoal animate-spin" />
                    ) : (
                      <Link size={24} className="text-zara-charcoal" />
                    )}
                  </div>
                  <div className="text-left flex-1">
                    <p className="zara-subtitle">Browse Files</p>
                    <p className="zara-body text-zara-dark-gray">Select from device</p>
                  </div>
                  {!isUploadingFile && (
                    <ChevronRight size={20} className="text-zara-medium-gray group-hover:translate-x-1 transition-transform duration-300" />
                  )}
                </button>
              </div>

              {/* Upload Error */}
              {uploadError && (
                <div className="mb-6 p-4 glass-subtle rounded-xl border border-red-300 flex items-center space-x-3">
                  <AlertCircle size={20} className="text-red-500 flex-shrink-0" />
                  <p className="text-red-500 zara-body">{uploadError}</p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <GlassButton
                  onClick={() => setIsAddModalOpen(false)}
                  variant="secondary"
                  className="flex-1"
                >
                  Cancel
                </GlassButton>
                <GlassButton
                  variant="primary"
                  className="flex-1"
                >
                  Continue
                </GlassButton>
              </div>
            </GlassCard>
          </div>
        </div>
      )}
    </div>
  );
};